<!-- 分润进度 -->
<template>
  <div>
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
      @rowDel="deleteRowHandler"
      @rowEdit="rowEdit"
      selectStyleType="infoTip"
      @handleBatchSelect="handleBatchSelect"
      :showSelectNum="showSelectNum"
    >
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['xdtCharge:profitProgress:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
      <template #toolbar_buttons>
        <el-select
          v-model="selectPage"
          size="mini"
          style="margin-right: 10px;width: 86px;"
        >
          <el-option label="当前页" value="1"></el-option>
          <el-option label="全部页" value="2"></el-option>
        </el-select>
        <el-button
          type="primary"
          @click="handleBatchImport"
          v-has-permi="['xdtCharge:profitProgress:import']"
          >导入</el-button
        >
        <el-button
          type="primary"
          @click="handleAdd"
          v-has-permi="['xdtCharge:profitProgress:add']"
          >新增</el-button
        >
        <el-button
          type="primary"
          @click="handleBatchAdd"
          v-has-permi="['xdtCharge:profitProgress:batchAdd']"
          >批量新增</el-button
        >
        <el-button
          type="primary"
          @click="handleBatchOperation"
          v-has-permi="['xdtCharge:profitProgress:batchOperation']"
          >批量操作</el-button
        >
      </template>
      <template #modalFooter="{ row, crudOperationType }">
        <div v-if="crudOperationType === 'log'"></div>
      </template>
      <template slot="log" slot-scope="{ row, operationType }">
        <Timeline
          :list="recordList"
          operateTypeTitle="operatorTypeName"
          operatorNameTitle="operatorUserName"
          createTimeTitle="operatorTime"
          operateDetailTitle="remark"
        ></Timeline>
      </template>
    </BuseCrud>
    <BatchUpload
      @uploadSuccess="handleResetAll"
      ref="batchUpload"
      title="批量导入分润进度"
      :uploadApi="uploadObj.api"
      :templateUrl="uploadObj.url"
      :extraData="uploadObj.extraData"
      :maxSize="0.01953"
      maxSizeText="20m"
    >
    </BatchUpload>

    <!-- 批量新增抽屉 -->
    <BatchAddDrawer
      ref="batchAddDrawer"
      title="批量新增分润结算进度"
      :columns="batchAddColumns"
      @submit="handleBatchAddSubmit"
    />
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/settlement/xdt/charge/profitProgress.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import Timeline from "@/components/Timeline/index.vue";
import BatchUpload from "@/components/BatchUpload/index.vue";
import { queryLog } from "@/api/common.js";
import BatchAddDrawer from "./components/BatchAddDrawer.vue";
export default {
  name: "profitProgress",
  mixins: [exportMixin],
  components: {
    Timeline,
    BatchUpload,
    BatchAddDrawer,
  },
  data() {
    return {
      selectPage: "1",
      workLoading: false,
      uploadObj: {
        api: "/st/newcharge/profit/importExcel",
        url: "/charging-maintenance-ui/static/分润进度导入模板.xlsx",
        extraData: {},
      },
      tableTotal: 0,
      // 查询参数
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      visible: false,
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "id",
          isCurrent: true,
        },
        checkboxConfig: {
          reserve: true,
        },
      },
      tableData: [],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      operationType: "update",
      isEdit: false,
      //buse参数-e

      // 下拉选项数据
      statusOptions: [],
      typeOptions: [],
      reconciliationPersonOptions: [],
      recordList: [],
      costTypeOptions: [{ dictLabel: "前端模拟", dictValue: "1" }],
      billReviewOptions: [],
      selectedData: [],
      returnCompleteOptions: [],
      counterpartySealOptions: [],
      ourSealOptions: [],
    };
  },
  created() {
    this.params = {
      ...initParams(this.filterOptions.config),
    };
    // 获取下拉列表数据
    this.loadDropListData();
  },
  methods: {
    checkPermission,
    handleBatchOperation() {
      if (this.selectPage == "1" && !this.selectedData.length) {
        this.$message.warning("请至少勾选一条数据");
        return;
      }
      this.operationType = "batchOperation";
      this.$refs.crud.switchModalView(true, "batchOperation", {
        ...initParams(this.modalConfig.formConfig),
        idList: this.selectedData?.map((x) => x.id),
        //todo 选择全部页/当前页 新加一个字段
        allPageFlag: this.selectPage == "2",
      });
    },
    handleBatchSelect(arr) {
      console.log(arr, "已选择");
      this.selectedData = arr;
    },

    handleBatchAdd() {
      this.$refs.batchAddDrawer.open();
    },

    // 处理批量新增提交
    async handleBatchAddSubmit(formData) {
      try {
        // 调用批量新增API
        await api.batchAdd(formData);
        this.$message.success("批量新增成功");
        this.$refs.batchAddDrawer.handleClose();
        this.loadData();
      } catch (error) {
        console.error("批量新增失败:", error);
        this.$message.error("批量新增失败，请重试");
      }
    },

    handleBatchImport() {
      this.$refs.batchUpload.open();
    },
    handleResetAll() {
      this.loadDropListData();
      this.handleQuery();
    },
    // 加载下拉列表数据
    async loadDropListData() {
      try {
        const res = await api.getDropLists();
        if (res.success && res.data) {
          if (res.success) {
            // 处理下拉列表数据
            if (res.data.reconciliationPerson) {
              this.reconciliationPersonOptions = res.data.reconciliationPerson.map(
                (item) => ({
                  dictLabel: item,
                  dictValue: item,
                })
              );
            }
          }
        }
      } catch (error) {
        console.error("获取下拉列表数据失败:", error);
      }
    },

    handleExport() {
      let params = {
        ...this.params,
      };
      this.handleTimeRange(params);
      this.handleCommonExport(api.export, params);
    },
    handleLog(row) {
      queryLog({ businessId: row.id }).then((res) => {
        this.recordList = res.data;
      });
      this.$refs.crud.switchModalView(true, "log");
    },
    handleAdd() {
      this.isEdit = false;
      this.operationType = "update";
      this.$refs.crud.switchModalView(true, "ADD", {
        ...initParams(this.modalConfig.formConfig),
      });
    },
    rowEdit(row) {
      this.isEdit = true;
      this.operationType = "update";
      this.$refs.crud.switchModalView(true, "UPDATE", {
        ...initParams(this.modalConfig.formConfig),
        ...row,
      });
    },
    deleteRowHandler(row) {
      this.$confirm("是否确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then((res) => {
        let params = {
          id: row.id,
        };
        api.delete(params).then((res) => {
          this.$message.success("删除成功");
          this.loadData();
        });
      });
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "billYearMonthRange",
          title: "账单年月",
          startFieldName: "billYearMonthStart",
          endFieldName: "billYearMonthEnd",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0];
          params[x.endFieldName] = params[x.field][1];
          delete params[x.field];
        }
      });
    },

    async loadData() {
      let params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };

      this.handleTimeRange(params);
      this.loading = true;
      const res = await api.list(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },

    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },

    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },

    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      let params = { ...formParams };
      console.log(crudOperationType, formParams, "提交");
      // crudOperationType: add/update/batchOperation
      try {
        await api[crudOperationType](params);
        this.$message.success("提交成功");
        this.loadData();
        crudOperationType === "batchOperation" &&
          this.$refs.crud.tableDeselectHandler();
      } catch (error) {
        console.error("操作失败:", error);
        return false;
      }
    },
  },
  computed: {
    showSelectNum() {
      return this.selectedData?.length > 0;
    },

    // 批量新增表格列配置
    batchAddColumns() {
      return [
        { type: "checkbox", width: 60, fixed: "left" },
        {
          title: "账单年月",
          field: "billYearMonth",
          width: 250,
          isEdit: true,
          element: "el-date-picker",
          rules: [{ required: true, message: "请选择账单年月" }],
          props: {
            type: "month",
            valueFormat: "yyyy-MM",
          },
        },
        {
          title: "运营商",
          field: "operator",
          width: 120,
          isEdit: true,
          element: "el-input",
          rules: [{ required: true, message: "请输入运营商" }],
        },
        {
          title: "费用类型",
          field: "costType",
          width: 120,
          isEdit: true,
          element: "el-select",
          props: {
            options: this.costTypeOptions,
            optionLabel: "dictLabel",
            optionValue: "dictValue",
            filterable: true,
          },
          rules: [{ required: true, message: "请选择费用类型" }],
        },
        {
          title: "分润收入（元）",
          field: "sharedIncome",
          width: 150,
          isEdit: true,
          element: "el-input-number",
          props: {
            precision: 2,
            min: 0,
          },
        },
        {
          title: "回票金额（元）",
          field: "returnAmount",
          width: 150,
          isEdit: true,
          element: "el-input-number",
          props: {
            precision: 2,
            min: 0,
          },
        },
        {
          title: "对账负责人",
          field: "reconciliationPerson",
          width: 120,
          isEdit: true,
          element: "el-input",
        },
        {
          title: "账单复核",
          field: "billReview",
          width: 120,
          isEdit: true,
          element: "el-select",
          props: {
            options: this.billReviewOptions,
            optionLabel: "dictLabel",
            optionValue: "dictValue",
            filterable: true,
          },
        },
        {
          title: "对方盖章",
          field: "counterpartySeal",
          width: 120,
          isEdit: true,
          element: "el-select",
          props: {
            options: this.counterpartySealOptions,
            optionLabel: "dictLabel",
            optionValue: "dictValue",
            filterable: true,
          },
        },
        {
          title: "我方盖章",
          field: "ourSeal",
          width: 120,
          isEdit: true,
          element: "el-select",
          props: {
            options: this.ourSealOptions,
            optionLabel: "dictLabel",
            optionValue: "dictValue",
            filterable: true,
          },
        },
        {
          title: "回票完成",
          field: "returnComplete",
          width: 120,
          isEdit: true,
          element: "el-select",
          props: {
            options: this.returnCompleteOptions,
            optionLabel: "dictLabel",
            optionValue: "dictValue",
            filterable: true,
          },
        },
        {
          title: "备注",
          field: "remarks",
          width: 200,
          isEdit: true,
          element: "el-input",
          props: {
            type: "textarea",
            rows: 2,
            maxlength: 500,
            showWordLimit: true,
            placeholder: "500个字符以内",
          },
        },
      ];
    },
    tableColumn() {
      return [
        { type: "checkbox", width: 60, fixed: "left" },
        {
          field: "billYearMonth",
          title: "账单年月",
          width: 120,
        },
        {
          field: "operator",
          title: "运营商",
          width: 150,
        },
        {
          field: "costType",
          title: "费用类型",
          width: 120,
        },
        {
          field: "sharedIncome",
          title: "分润收入（元）",
          width: 120,
        },
        {
          field: "returnAmount",
          title: "回票金额（元）",
          width: 120,
        },
        {
          field: "reconciliationPerson",
          title: "对账负责人",
          width: 100,
        },
        {
          field: "billReview",
          title: "账单复核",
          width: 100,
        },
        {
          field: "counterpartySeal",
          title: "对方盖章",
          width: 100,
        },
        {
          field: "ourSeal",
          title: "我方盖章",
          width: 100,
        },
        {
          field: "returnComplete",
          title: "回票完成",
          width: 100,
        },
        {
          field: "remarks",
          title: "备注",
          width: 150,
        },
        {
          field: "mode",
          title: "模式",
          width: 100,
        },
        {
          field: "updateBy",
          title: "操作人",
          width: 100,
        },
        {
          field: "updateTime",
          title: "操作时间",
          width: 160,
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "140px",
        //筛选控件配置
        config: [
          {
            field: "operator",
            element: "el-input",
            title: "运营商",
          },
          {
            field: "reconciliationPerson",
            element: "el-select",
            title: "负责人",
            props: {
              options: this.reconciliationPersonOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "ourSeal",
            title: "我方盖章",
            element: "el-select",
            props: {
              options: this.ourSealOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "billYearMonthRange",
            title: "账单年月",
            element: "el-date-picker",
            props: {
              type: "monthrange",
              valueFormat: "yyyy-MM",
            },
          },
          {
            field: "billReview",
            title: "账单复核",
            element: "el-select",
            props: {
              options: this.billReviewOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "counterpartySeal",
            title: "对方盖章",
            element: "el-select",
            props: {
              options: this.counterpartySealOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "returnComplete",
            title: "回票完成",
            element: "el-select",
            props: {
              options: this.returnCompleteOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
        ],
        params: this.params,
      };
    },

    modalConfig() {
      const form = {
        update: [
          {
            field: "billYearMonth",
            title: "账单年月",
            element: "el-date-picker",
            rules: [{ required: true, message: "请选择账单年月" }],
            props: {
              type: "month",
              valueFormat: "yyyy-MM",
            },
            preview: this.isEdit,
          },
          {
            field: "operator",
            title: "运营商",
            element: "el-input",
            rules: [{ required: true, message: "请输入运营商" }],
            preview: this.isEdit,
          },
          {
            field: "sharedIncome",
            title: "分润收入（元）",
            element: "el-input-number",
            props: {
              precision: 2,
              min: 0,
            },
            preview: this.isEdit,
            // previewFormatter: (val) => val + "元",
          },
          {
            field: "returnAmount",
            title: "回票金额（元）",
            element: "el-input-number",
            props: {
              precision: 2,
              min: 0,
            },
          },
          {
            field: "reconciliationPerson",
            title: "对账负责人",
            element: "el-input",
          },
          {
            field: "costType",
            title: "费用类型",
            element: "el-select",
            props: {
              options: this.costTypeOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
            },
            rules: [{ required: true, message: "请选择费用类型" }],
          },
          {
            field: "billReview",
            title: "账单复核",
            element: "el-select",
            props: {
              options: this.billReviewOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
            },
          },
          {
            field: "counterpartySeal",
            title: "对方盖章",
            element: "el-select",
            props: {
              options: this.counterpartySealOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
            },
          },
          {
            field: "ourSeal",
            title: "我方盖章",
            element: "el-select",
            props: {
              options: this.ourSealOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
            },
          },
          {
            field: "returnComplete",
            title: "回票完成",
            element: "el-select",
            props: {
              options: this.returnCompleteOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
            },
          },
          {
            field: "remarks",
            title: "备注",
            element: "el-input",
            props: {
              type: "textarea",
              rows: 5,
              maxlength: 500,
              showWordLimit: true,
              placeholder: "500个字符以内",
            },
          },
        ],
        batchOperation: [
          {
            field: "costType",
            title: "费用类型",
            element: "el-select",
            props: {
              options: this.costTypeOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
            },
            rules: [{ required: true, message: "请选择费用类型" }],
          },
          {
            field: "billReview",
            title: "账单复核",
            element: "el-select",
            props: {
              options: this.billReviewOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
            },
          },
          {
            field: "counterpartySeal",
            title: "对方盖章",
            element: "el-select",
            props: {
              options: this.counterpartySealOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
            },
          },
          {
            field: "ourSeal",
            title: "我方盖章",
            element: "el-select",
            props: {
              options: this.ourSealOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
            },
          },
          {
            field: "returnComplete",
            title: "回票完成",
            element: "el-select",
            props: {
              options: this.returnCompleteOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
            },
          },
          {
            field: "remarks",
            title: "备注",
            element: "el-input",
            props: {
              type: "textarea",
              rows: 5,
              maxlength: 500,
              showWordLimit: true,
              placeholder: "500个字符以内",
            },
          },
        ],
      };
      return {
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        editBtn: checkPermission(["xdtCharge:profitProgress:edit"]),
        delBtn: checkPermission(["xdtCharge:profitProgress:delete"]),
        menu: true,
        menuWidth: 250,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: form[this.operationType],
        crudPermission: [],
        customOperationTypes: [
          {
            title: "日志",
            typeName: "log",
            slotName: "log",
            showForm: false,
            event: (row) => {
              return this.handleLog(row);
            },
            condition: (row) => {
              return checkPermission(["xdtCharge:profitProgress:log"]);
            },
          },
          //非操作列
          {
            title: "批量操作",
            typeName: "batchOperation",
            slotName: "batchOperation",
            condition: (row) => {
              return false;
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
          labelWidth: "120px",
        },
      };
    },
  },
};
</script>

<style></style>
